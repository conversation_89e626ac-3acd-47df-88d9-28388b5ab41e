CRITICAL - 2024-01-22 09:35:42 --> CodeIgniter\HTTP\Exceptions\HTTPException: Failed to parse JSON string. Error: Syntax error
in SYSTEMPATH/HTTP/IncomingRequest.php on line 588.
 1 SYSTEMPATH/HTTP/IncomingRequest.php(588): CodeIgniter\HTTP\Exceptions\HTTPException::forInvalidJSON()
 2 APPPATH/Controllers/UsersController.php(15): CodeIgniter\HTTP\IncomingRequest->getJSON()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 09:40:44 --> CodeIgniter\HTTP\Exceptions\HTTPException: Failed to parse JSON string. Error: Syntax error
in SYSTEMPATH/HTTP/IncomingRequest.php on line 588.
 1 SYSTEMPATH/HTTP/IncomingRequest.php(588): CodeIgniter\HTTP\Exceptions\HTTPException::forInvalidJSON()
 2 APPPATH/Controllers/UsersController.php(15): CodeIgniter\HTTP\IncomingRequest->getJSON()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
INFO - 2024-01-22 09:51:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 09:51:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 09:53:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 09:54:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:01:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:01:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:03:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:03:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:03:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:04:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:04:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:05:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:05:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:19:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:19:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:19:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:20:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:21:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:21:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:22:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:22:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:22:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:22:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:27:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:27:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:30:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:30:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:31:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:31:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:32:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:33:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:33:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:34:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:34:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:54:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-01-22 10:56:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2024-01-22 10:56:56 --> mysqli_sql_exception: Unknown column 'passwor' in 'where clause' in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:306
Stack trace:
#0 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(306): mysqli->query()
#1 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
#4 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Controllers/UsersController.php(28): CodeIgniter\Database\BaseBuilder->getWhere()
#5 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(941): App\Controllers\UsersController->login()
#6 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
#7 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
#8 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php(79): CodeIgniter\CodeIgniter->run()
#9 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(47): require_once('...')
#10 {main}
CRITICAL - 2024-01-22 10:56:56 --> Error: Call to a member function getRowArray() on bool
in APPPATH/Controllers/UsersController.php on line 29.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
INFO - 2024-01-22 10:57:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2024-01-22 10:57:21 --> mysqli_sql_exception: Unknown column 'passwor' in 'where clause' in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:306
Stack trace:
#0 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(306): mysqli->query()
#1 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
#4 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Controllers/UsersController.php(28): CodeIgniter\Database\BaseBuilder->getWhere()
#5 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(941): App\Controllers\UsersController->login()
#6 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
#7 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
#8 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php(79): CodeIgniter\CodeIgniter->run()
#9 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(47): require_once('...')
#10 {main}
CRITICAL - 2024-01-22 10:57:21 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'passwor' in 'where clause'
in SYSTEMPATH/Database/BaseConnection.php on line 647.
 1 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 2 APPPATH/Controllers/UsersController.php(28): CodeIgniter\Database\BaseBuilder->getWhere()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 10:57:21 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'passwor' in 'where clause'
in SYSTEMPATH/Database/MySQLi/Connection.php on line 311.
 1 SYSTEMPATH/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
 2 SYSTEMPATH/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
 3 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 4 APPPATH/Controllers/UsersController.php(28): CodeIgniter\Database\BaseBuilder->getWhere()
 5 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 6 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 7 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 8 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 10:57:21 --> [Caused by] mysqli_sql_exception: Unknown column 'passwor' in 'where clause'
in SYSTEMPATH/Database/MySQLi/Connection.php on line 306.
 1 SYSTEMPATH/Database/MySQLi/Connection.php(306): mysqli->query()
 2 SYSTEMPATH/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
 3 SYSTEMPATH/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
 4 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 5 APPPATH/Controllers/UsersController.php(28): CodeIgniter\Database\BaseBuilder->getWhere()
 6 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 7 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 8 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 9 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
ERROR - 2024-01-22 11:18:46 --> mysqli_sql_exception: Unknown column 'passwor' in 'where clause' in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:306
Stack trace:
#0 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(306): mysqli->query()
#1 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
#4 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Controllers/UsersController.php(24): CodeIgniter\Database\BaseBuilder->getWhere()
#5 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(941): App\Controllers\UsersController->login()
#6 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
#7 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
#8 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php(79): CodeIgniter\CodeIgniter->run()
#9 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(47): require_once('...')
#10 {main}
CRITICAL - 2024-01-22 11:18:46 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'passwor' in 'where clause'
in SYSTEMPATH/Database/BaseConnection.php on line 647.
 1 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 2 APPPATH/Controllers/UsersController.php(24): CodeIgniter\Database\BaseBuilder->getWhere()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 11:18:46 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'passwor' in 'where clause'
in SYSTEMPATH/Database/MySQLi/Connection.php on line 311.
 1 SYSTEMPATH/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
 2 SYSTEMPATH/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
 3 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 4 APPPATH/Controllers/UsersController.php(24): CodeIgniter\Database\BaseBuilder->getWhere()
 5 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 6 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 7 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 8 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 11:18:46 --> [Caused by] mysqli_sql_exception: Unknown column 'passwor' in 'where clause'
in SYSTEMPATH/Database/MySQLi/Connection.php on line 306.
 1 SYSTEMPATH/Database/MySQLi/Connection.php(306): mysqli->query()
 2 SYSTEMPATH/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
 3 SYSTEMPATH/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
 4 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 5 APPPATH/Controllers/UsersController.php(24): CodeIgniter\Database\BaseBuilder->getWhere()
 6 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 7 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 8 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 9 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 11:18:55 --> InvalidArgumentException: key must be a string when using hmac
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 249.
 1 VENDORPATH/firebase/php-jwt/src/JWT.php(219): Firebase\JWT\JWT::sign()
 2 APPPATH/Controllers/UsersController.php(39): Firebase\JWT\JWT::encode()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 11:19:57 --> InvalidArgumentException: key must be a string when using hmac
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 249.
 1 VENDORPATH/firebase/php-jwt/src/JWT.php(219): Firebase\JWT\JWT::sign()
 2 APPPATH/Controllers/UsersController.php(39): Firebase\JWT\JWT::encode()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 11:20:01 --> InvalidArgumentException: key must be a string when using hmac
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 249.
 1 VENDORPATH/firebase/php-jwt/src/JWT.php(219): Firebase\JWT\JWT::sign()
 2 APPPATH/Controllers/UsersController.php(39): Firebase\JWT\JWT::encode()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 11:20:12 --> InvalidArgumentException: key must be a string when using hmac
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 249.
 1 VENDORPATH/firebase/php-jwt/src/JWT.php(219): Firebase\JWT\JWT::sign()
 2 APPPATH/Controllers/UsersController.php(39): Firebase\JWT\JWT::encode()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 11:20:23 --> InvalidArgumentException: key must be a string when using hmac
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 249.
 1 VENDORPATH/firebase/php-jwt/src/JWT.php(219): Firebase\JWT\JWT::sign()
 2 APPPATH/Controllers/UsersController.php(39): Firebase\JWT\JWT::encode()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
ERROR - 2024-01-22 11:42:01 --> mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" or sleep(1) -- - = 'a'
 LIMIT 1' at line 4 in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:306
Stack trace:
#0 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(306): mysqli->query()
#1 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
#4 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
#5 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(941): App\Controllers\UsersController->login()
#6 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
#7 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
#8 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php(79): CodeIgniter\CodeIgniter->run()
#9 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(47): require_once('...')
#10 {main}
CRITICAL - 2024-01-22 11:42:02 --> CodeIgniter\Database\Exceptions\DatabaseException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" or sleep(1) -- - = 'a'
 LIMIT 1' at line 4
in SYSTEMPATH/Database/BaseConnection.php on line 647.
 1 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 2 APPPATH/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 11:42:02 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" or sleep(1) -- - = 'a'
 LIMIT 1' at line 4
in SYSTEMPATH/Database/MySQLi/Connection.php on line 311.
 1 SYSTEMPATH/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
 2 SYSTEMPATH/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
 3 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 4 APPPATH/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
 5 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 6 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 7 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 8 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 11:42:02 --> [Caused by] mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" or sleep(1) -- - = 'a'
 LIMIT 1' at line 4
in SYSTEMPATH/Database/MySQLi/Connection.php on line 306.
 1 SYSTEMPATH/Database/MySQLi/Connection.php(306): mysqli->query()
 2 SYSTEMPATH/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
 3 SYSTEMPATH/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
 4 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 5 APPPATH/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
 6 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 7 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 8 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 9 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
ERROR - 2024-01-22 11:42:05 --> mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" or sleep(2) -- - = 'a'
 LIMIT 1' at line 4 in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:306
Stack trace:
#0 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(306): mysqli->query()
#1 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
#4 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
#5 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(941): App\Controllers\UsersController->login()
#6 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
#7 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
#8 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php(79): CodeIgniter\CodeIgniter->run()
#9 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(47): require_once('...')
#10 {main}
CRITICAL - 2024-01-22 11:42:05 --> CodeIgniter\Database\Exceptions\DatabaseException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" or sleep(2) -- - = 'a'
 LIMIT 1' at line 4
in SYSTEMPATH/Database/BaseConnection.php on line 647.
 1 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 2 APPPATH/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 11:42:05 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" or sleep(2) -- - = 'a'
 LIMIT 1' at line 4
in SYSTEMPATH/Database/MySQLi/Connection.php on line 311.
 1 SYSTEMPATH/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
 2 SYSTEMPATH/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
 3 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 4 APPPATH/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
 5 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 6 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 7 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 8 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 11:42:05 --> [Caused by] mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" or sleep(2) -- - = 'a'
 LIMIT 1' at line 4
in SYSTEMPATH/Database/MySQLi/Connection.php on line 306.
 1 SYSTEMPATH/Database/MySQLi/Connection.php(306): mysqli->query()
 2 SYSTEMPATH/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
 3 SYSTEMPATH/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
 4 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 5 APPPATH/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
 6 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 7 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 8 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 9 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
ERROR - 2024-01-22 11:42:15 --> mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" or select(sleep(2)) -- - = 'a'
 LIMIT 1' at line 4 in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:306
Stack trace:
#0 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(306): mysqli->query()
#1 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
#4 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
#5 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(941): App\Controllers\UsersController->login()
#6 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
#7 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
#8 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php(79): CodeIgniter\CodeIgniter->run()
#9 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(47): require_once('...')
#10 {main}
CRITICAL - 2024-01-22 11:42:15 --> CodeIgniter\Database\Exceptions\DatabaseException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" or select(sleep(2)) -- - = 'a'
 LIMIT 1' at line 4
in SYSTEMPATH/Database/BaseConnection.php on line 647.
 1 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 2 APPPATH/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 11:42:15 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" or select(sleep(2)) -- - = 'a'
 LIMIT 1' at line 4
in SYSTEMPATH/Database/MySQLi/Connection.php on line 311.
 1 SYSTEMPATH/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
 2 SYSTEMPATH/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
 3 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 4 APPPATH/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
 5 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 6 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 7 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 8 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 11:42:15 --> [Caused by] mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" or select(sleep(2)) -- - = 'a'
 LIMIT 1' at line 4
in SYSTEMPATH/Database/MySQLi/Connection.php on line 306.
 1 SYSTEMPATH/Database/MySQLi/Connection.php(306): mysqli->query()
 2 SYSTEMPATH/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
 3 SYSTEMPATH/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
 4 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 5 APPPATH/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
 6 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 7 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 8 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 9 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
ERROR - 2024-01-22 11:42:20 --> mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'select(sleep(2)) -- -:
 LIMIT 1' at line 4 in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:306
Stack trace:
#0 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(306): mysqli->query()
#1 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
#4 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
#5 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(941): App\Controllers\UsersController->login()
#6 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
#7 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
#8 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php(79): CodeIgniter\CodeIgniter->run()
#9 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(47): require_once('...')
#10 {main}
CRITICAL - 2024-01-22 11:42:20 --> CodeIgniter\Database\Exceptions\DatabaseException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'select(sleep(2)) -- -:
 LIMIT 1' at line 4
in SYSTEMPATH/Database/BaseConnection.php on line 647.
 1 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 2 APPPATH/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 11:42:20 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'select(sleep(2)) -- -:
 LIMIT 1' at line 4
in SYSTEMPATH/Database/MySQLi/Connection.php on line 311.
 1 SYSTEMPATH/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
 2 SYSTEMPATH/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
 3 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 4 APPPATH/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
 5 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 6 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 7 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 8 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 11:42:20 --> [Caused by] mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'select(sleep(2)) -- -:
 LIMIT 1' at line 4
in SYSTEMPATH/Database/MySQLi/Connection.php on line 306.
 1 SYSTEMPATH/Database/MySQLi/Connection.php(306): mysqli->query()
 2 SYSTEMPATH/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
 3 SYSTEMPATH/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
 4 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 5 APPPATH/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
 6 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 7 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 8 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 9 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
ERROR - 2024-01-22 11:42:23 --> mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5 in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:306
Stack trace:
#0 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(306): mysqli->query()
#1 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
#4 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
#5 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(941): App\Controllers\UsersController->login()
#6 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
#7 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
#8 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php(79): CodeIgniter\CodeIgniter->run()
#9 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(47): require_once('...')
#10 {main}
CRITICAL - 2024-01-22 11:42:23 --> CodeIgniter\Database\Exceptions\DatabaseException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5
in SYSTEMPATH/Database/BaseConnection.php on line 647.
 1 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 2 APPPATH/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 11:42:23 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5
in SYSTEMPATH/Database/MySQLi/Connection.php on line 311.
 1 SYSTEMPATH/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
 2 SYSTEMPATH/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
 3 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 4 APPPATH/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
 5 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 6 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 7 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 8 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 11:42:23 --> [Caused by] mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5
in SYSTEMPATH/Database/MySQLi/Connection.php on line 306.
 1 SYSTEMPATH/Database/MySQLi/Connection.php(306): mysqli->query()
 2 SYSTEMPATH/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
 3 SYSTEMPATH/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
 4 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 5 APPPATH/Controllers/UsersController.php(22): CodeIgniter\Database\BaseBuilder->getWhere()
 6 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 7 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 8 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 9 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:06:12 --> ErrorException: Undefined array key "token"
in APPPATH/Views/IndexPage.php on line 30.
 1 APPPATH/Views/IndexPage.php(30): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/View/View.php(228): include('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Views/IndexPage.php')
 3 SYSTEMPATH/View/View.php(231): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH/Common.php(1178): CodeIgniter\View\View->render()
 5 APPPATH/Controllers/Home.php(9): view()
 6 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 7 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 8 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 9 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:06:31 --> ErrorException: Undefined array key "token"
in APPPATH/Views/IndexPage.php on line 32.
 1 APPPATH/Views/IndexPage.php(32): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/View/View.php(228): include('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Views/IndexPage.php')
 3 SYSTEMPATH/View/View.php(231): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH/Common.php(1178): CodeIgniter\View\View->render()
 5 APPPATH/Controllers/Home.php(9): view()
 6 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 7 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 8 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 9 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:09:03 --> InvalidArgumentException: Key material must not be empty
in VENDORPATH/firebase/php-jwt/src/Key.php on line 35.
 1 APPPATH/Views/IndexPage.php(34): Firebase\JWT\Key->__construct()
 2 SYSTEMPATH/View/View.php(228): include('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Views/IndexPage.php')
 3 SYSTEMPATH/View/View.php(231): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH/Common.php(1178): CodeIgniter\View\View->render()
 5 APPPATH/Controllers/Home.php(9): view()
 6 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 7 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 8 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 9 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:09:25 --> InvalidArgumentException: Key material must not be empty
in VENDORPATH/firebase/php-jwt/src/Key.php on line 35.
 1 APPPATH/Views/IndexPage.php(35): Firebase\JWT\Key->__construct()
 2 SYSTEMPATH/View/View.php(228): include('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Views/IndexPage.php')
 3 SYSTEMPATH/View/View.php(231): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH/Common.php(1178): CodeIgniter\View\View->render()
 5 APPPATH/Controllers/Home.php(9): view()
 6 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 7 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 8 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 9 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:09:34 --> InvalidArgumentException: Key material must not be empty
in VENDORPATH/firebase/php-jwt/src/Key.php on line 35.
 1 APPPATH/Views/IndexPage.php(35): Firebase\JWT\Key->__construct()
 2 SYSTEMPATH/View/View.php(228): include('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Views/IndexPage.php')
 3 SYSTEMPATH/View/View.php(231): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH/Common.php(1178): CodeIgniter\View\View->render()
 5 APPPATH/Controllers/Home.php(9): view()
 6 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 7 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 8 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 9 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:15:04 --> Error: Class "App\Controllers\Key" not found
in APPPATH/Controllers/Home.php on line 14.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:16:06 --> InvalidArgumentException: Key material must not be empty
in VENDORPATH/firebase/php-jwt/src/Key.php on line 35.
 1 APPPATH/Controllers/Home.php(14): Firebase\JWT\Key->__construct()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:16:23 --> ErrorException: Undefined variable $decoded
in APPPATH/Controllers/Home.php on line 15.
 1 APPPATH/Controllers/Home.php(15): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:16:58 --> Error: Class "App\Controllers\Key" not found
in APPPATH/Controllers/Home.php on line 14.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:18:45 --> Error: Class "App\Controllers\Key" not found
in APPPATH/Controllers/Home.php on line 14.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:18:53 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 150.
 1 APPPATH/Controllers/Home.php(14): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:19:14 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 150.
 1 APPPATH/Controllers/Home.php(14): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:19:20 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 150.
 1 APPPATH/Controllers/Home.php(14): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:20:32 --> Error: Firebase\JWT\JWT::decode(): Argument #3 ($headers) cannot be passed by reference
in APPPATH/Controllers/Home.php on line 14.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:21:32 --> Error: Firebase\JWT\JWT::decode(): Argument #3 ($headers) cannot be passed by reference
in APPPATH/Controllers/Home.php on line 14.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:24:12 --> UnexpectedValueException: "kid" empty, unable to lookup correct key
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 443.
 1 VENDORPATH/firebase/php-jwt/src/JWT.php(133): Firebase\JWT\JWT::getKey()
 2 APPPATH/Controllers/Home.php(14): Firebase\JWT\JWT::decode()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:25:33 --> Error: Firebase\JWT\JWT::decode(): Argument #3 ($headers) cannot be passed by reference
in APPPATH/Controllers/Home.php on line 14.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:26:25 --> Error: Class "App\Controllers\stdClass" not found
in APPPATH/Controllers/Home.php on line 14.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:27:29 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 150.
 1 APPPATH/Controllers/Home.php(14): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:27:38 --> ErrorException: Undefined variable $decoded
in APPPATH/Controllers/Home.php on line 17.
 1 APPPATH/Controllers/Home.php(17): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:28:30 --> ErrorException: Undefined variable $decoded
in APPPATH/Controllers/Home.php on line 17.
 1 APPPATH/Controllers/Home.php(17): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:28:50 --> ErrorException: Undefined variable $data
in APPPATH/Controllers/Home.php on line 19.
 1 APPPATH/Controllers/Home.php(19): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:29:02 --> ErrorException: Undefined variable $data
in APPPATH/Controllers/Home.php on line 19.
 1 APPPATH/Controllers/Home.php(19): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:29:23 --> ErrorException: Undefined array key "jwt_data"
in APPPATH/Controllers/Home.php on line 19.
 1 APPPATH/Controllers/Home.php(19): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:29:37 --> ErrorException: Undefined variable $jwt_data
in APPPATH/Views/IndexPage.php on line 28.
 1 APPPATH/Views/IndexPage.php(28): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/View/View.php(228): include('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Views/IndexPage.php')
 3 SYSTEMPATH/View/View.php(231): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH/Common.php(1178): CodeIgniter\View\View->render()
 5 APPPATH/Controllers/Home.php(20): view()
 6 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 7 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 8 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 9 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:30:22 --> ErrorException: Undefined variable $jwt_data
in APPPATH/Views/IndexPage.php on line 28.
 1 APPPATH/Views/IndexPage.php(28): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/View/View.php(228): include('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Views/IndexPage.php')
 3 SYSTEMPATH/View/View.php(231): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH/Common.php(1178): CodeIgniter\View\View->render()
 5 APPPATH/Controllers/Home.php(20): view()
 6 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 7 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 8 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 9 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:31:47 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 150.
 1 APPPATH/Controllers/Home.php(14): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:33:44 --> UnexpectedValueException: "kid" empty, unable to lookup correct key
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 470.
 1 VENDORPATH/firebase/php-jwt/src/JWT.php(138): Firebase\JWT\JWT::getKey()
 2 APPPATH/Controllers/Home.php(14): Firebase\JWT\JWT::decode()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:35:34 --> UnexpectedValueException: "kid" empty, unable to lookup correct key
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 433.
 1 VENDORPATH/firebase/php-jwt/src/JWT.php(128): Firebase\JWT\JWT::getKey()
 2 APPPATH/Controllers/Home.php(14): Firebase\JWT\JWT::decode()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:36:14 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 140.
 1 APPPATH/Controllers/Home.php(14): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:36:25 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 140.
 1 APPPATH/Controllers/Home.php(14): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 12:36:44 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 140.
 1 APPPATH/Controllers/Home.php(14): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 13:28:31 --> ParseError: syntax error, unexpected token "}"
in APPPATH/Controllers/Home.php on line 22.
 1 SYSTEMPATH/Autoloader/Autoloader.php(288): CodeIgniter\Autoloader\Autoloader->includeFile()
 2 SYSTEMPATH/Autoloader/Autoloader.php(266): CodeIgniter\Autoloader\Autoloader->loadInNamespace()
 3 [internal function]: CodeIgniter\Autoloader\Autoloader->loadClass()
 4 SYSTEMPATH/CodeIgniter.php(899): class_exists()
 5 SYSTEMPATH/CodeIgniter.php(489): CodeIgniter\CodeIgniter->startController()
 6 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 7 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 13:28:43 --> ErrorException: Undefined variable $username
in APPPATH/Controllers/Home.php on line 23.
 1 APPPATH/Controllers/Home.php(23): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 13:29:46 --> ErrorException: Undefined variable $username
in APPPATH/Controllers/Home.php on line 23.
 1 APPPATH/Controllers/Home.php(23): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 13:37:38 --> ErrorException: Undefined variable $payload
in APPPATH/Views/IndexPage.php on line 33.
 1 APPPATH/Views/IndexPage.php(33): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/View/View.php(228): include('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Views/IndexPage.php')
 3 SYSTEMPATH/View/View.php(231): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH/Common.php(1178): CodeIgniter\View\View->render()
 5 APPPATH/Controllers/Home.php(27): view()
 6 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 7 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 8 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 9 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 13:41:57 --> TypeError: App\Controllers\Home::index(): Return value must be of type string, none returned
in APPPATH/Controllers/Home.php on line 27.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 13:42:06 --> ParseError: syntax error, unexpected token "{"
in APPPATH/Controllers/Home.php on line 12.
 1 SYSTEMPATH/Autoloader/Autoloader.php(288): CodeIgniter\Autoloader\Autoloader->includeFile()
 2 SYSTEMPATH/Autoloader/Autoloader.php(266): CodeIgniter\Autoloader\Autoloader->loadInNamespace()
 3 [internal function]: CodeIgniter\Autoloader\Autoloader->loadClass()
 4 SYSTEMPATH/CodeIgniter.php(899): class_exists()
 5 SYSTEMPATH/CodeIgniter.php(489): CodeIgniter\CodeIgniter->startController()
 6 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 7 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 13:43:29 --> ErrorException: Undefined variable $jwt
in APPPATH/Controllers/Home.php on line 18.
 1 APPPATH/Controllers/Home.php(18): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 13:44:06 --> ErrorException: Trying to access array offset on value of type null
in APPPATH/Controllers/Home.php on line 21.
 1 APPPATH/Controllers/Home.php(21): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 13:44:15 --> ErrorException: Trying to access array offset on value of type null
in APPPATH/Controllers/Home.php on line 21.
 1 APPPATH/Controllers/Home.php(21): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 13:45:17 --> ErrorException: Undefined variable $username
in APPPATH/Controllers/Home.php on line 27.
 1 APPPATH/Controllers/Home.php(27): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 13:50:48 --> ErrorException: Undefined property: stdClass::$exp
in APPPATH/Libraries/JwtLibrary.php on line 43.
 1 APPPATH/Libraries/JwtLibrary.php(43): CodeIgniter\Debug\Exceptions->errorHandler()
 2 APPPATH/Controllers/Home.php(22): App\Libraries\JwtLibrary->is_jwt_valid()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 13:50:57 --> ErrorException: Undefined property: stdClass::$exp
in APPPATH/Libraries/JwtLibrary.php on line 43.
 1 APPPATH/Libraries/JwtLibrary.php(43): CodeIgniter\Debug\Exceptions->errorHandler()
 2 APPPATH/Controllers/Home.php(22): App\Libraries\JwtLibrary->is_jwt_valid()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 13:51:00 --> ErrorException: Undefined property: stdClass::$exp
in APPPATH/Libraries/JwtLibrary.php on line 43.
 1 APPPATH/Libraries/JwtLibrary.php(43): CodeIgniter\Debug\Exceptions->errorHandler()
 2 APPPATH/Controllers/Home.php(22): App\Libraries\JwtLibrary->is_jwt_valid()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 13:54:53 --> ParseError: Unclosed '{' on line 6
in APPPATH/Controllers/Home.php on line 48.
 1 SYSTEMPATH/Autoloader/Autoloader.php(288): CodeIgniter\Autoloader\Autoloader->includeFile()
 2 SYSTEMPATH/Autoloader/Autoloader.php(266): CodeIgniter\Autoloader\Autoloader->loadInNamespace()
 3 [internal function]: CodeIgniter\Autoloader\Autoloader->loadClass()
 4 SYSTEMPATH/CodeIgniter.php(899): class_exists()
 5 SYSTEMPATH/CodeIgniter.php(489): CodeIgniter\CodeIgniter->startController()
 6 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 7 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
WARNING - 2024-01-22 13:56:23 --> [DEPRECATED] explode(): Passing null to parameter #2 ($string) of type string is deprecated in APPPATH/Controllers/Home.php on line 14.
 1 APPPATH/Controllers/Home.php(14): explode('.', null)
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 13:56:23 --> ErrorException: Undefined array key 1
in APPPATH/Controllers/Home.php on line 16.
 1 APPPATH/Controllers/Home.php(16): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 13:58:45 --> ParseError: Unclosed '{' on line 11 does not match ')'
in APPPATH/Controllers/Home.php on line 22.
 1 SYSTEMPATH/Autoloader/Autoloader.php(288): CodeIgniter\Autoloader\Autoloader->includeFile()
 2 SYSTEMPATH/Autoloader/Autoloader.php(266): CodeIgniter\Autoloader\Autoloader->loadInNamespace()
 3 [internal function]: CodeIgniter\Autoloader\Autoloader->loadClass()
 4 SYSTEMPATH/CodeIgniter.php(899): class_exists()
 5 SYSTEMPATH/CodeIgniter.php(489): CodeIgniter\CodeIgniter->startController()
 6 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 7 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 14:09:16 --> ArgumentCountError: Too few arguments to function App\Libraries\JwtLibrary::is_jwt_valid(), 1 passed in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Controllers/UsersController.php on line 44 and exactly 2 expected
in APPPATH/Libraries/JwtLibrary.php on line 31.
 1 APPPATH/Controllers/UsersController.php(44): App\Libraries\JwtLibrary->is_jwt_valid()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 14:09:32 --> ArgumentCountError: Too few arguments to function App\Libraries\JwtLibrary::is_jwt_valid(), 1 passed in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Controllers/UsersController.php on line 44 and exactly 2 expected
in APPPATH/Libraries/JwtLibrary.php on line 31.
 1 APPPATH/Controllers/UsersController.php(44): App\Libraries\JwtLibrary->is_jwt_valid()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 14:13:09 --> ParseError: syntax error, unexpected token ":", expecting "]"
in APPPATH/Controllers/UsersController.php on line 48.
 1 SYSTEMPATH/Autoloader/Autoloader.php(288): CodeIgniter\Autoloader\Autoloader->includeFile()
 2 SYSTEMPATH/Autoloader/Autoloader.php(266): CodeIgniter\Autoloader\Autoloader->loadInNamespace()
 3 [internal function]: CodeIgniter\Autoloader\Autoloader->loadClass()
 4 SYSTEMPATH/CodeIgniter.php(899): class_exists()
 5 SYSTEMPATH/CodeIgniter.php(489): CodeIgniter\CodeIgniter->startController()
 6 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 7 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 15:59:31 --> ValueError: hash_hmac(): Argument #1 ($algo) must be a valid cryptographic hashing algorithm
in APPPATH/Libraries/JwtLibrary.php on line 49.
 1 APPPATH/Libraries/JwtLibrary.php(49): hash_hmac()
 2 APPPATH/Controllers/UsersController.php(44): App\Libraries\JwtLibrary->is_jwt_valid()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UsersController->login()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 16:12:46 --> Error: Class "App\Controllers\JWT" not found
in APPPATH/Controllers/Home.php on line 11.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 16:12:49 --> Error: Class "App\Controllers\JWT" not found
in APPPATH/Controllers/Home.php on line 11.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 16:13:02 --> InvalidArgumentException: Key material must not be empty
in VENDORPATH/firebase/php-jwt/src/Key.php on line 35.
 1 APPPATH/Controllers/Home.php(12): Firebase\JWT\Key->__construct()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 16:13:27 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 140.
 1 APPPATH/Controllers/Home.php(12): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 16:17:28 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Controllers/Home.php(12): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 16:19:54 --> ErrorException: Undefined array key "token"
in APPPATH/Controllers/Home.php on line 10.
 1 APPPATH/Controllers/Home.php(10): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 16:22:12 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Controllers/Home.php(13): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 16:22:24 --> Error: Cannot use object of type stdClass as array
in APPPATH/Controllers/Home.php on line 14.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 16:23:57 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Controllers/Home.php(13): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 16:24:09 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Controllers/Home.php(13): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 16:25:13 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Controllers/Home.php(13): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 16:27:29 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Controllers/Home.php(13): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 16:27:46 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Controllers/Home.php(13): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 16:27:51 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Controllers/Home.php(13): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 16:32:30 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Controllers/Home.php(13): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\Home->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 16:49:57 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Controllers/ProfileController.php(17): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 16:56:01 --> ErrorException: Undefined array key "token"
in APPPATH/Controllers/ProfileController.php on line 14.
 1 APPPATH/Controllers/ProfileController.php(14): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 16:56:21 --> ErrorException: Undefined array key "token"
in APPPATH/Controllers/ProfileController.php on line 14.
 1 APPPATH/Controllers/ProfileController.php(14): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 17:04:41 --> Error: Call to undefined function App\Controllers\str()
in APPPATH/Controllers/UserController.php on line 20.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UserController->login()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-22 17:09:58 --> UnexpectedValueException: Wrong number of segments
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 102.
 1 APPPATH/Controllers/ProfileController.php(18): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
