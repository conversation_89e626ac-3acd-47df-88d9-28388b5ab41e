CRITICAL - 2024-01-23 08:55:23 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 140.
 1 APPPATH/Filters/AuthenticatedFilter.php(40): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 08:55:37 --> ErrorException: Undefined array key "token"
in APPPATH/Filters/AuthenticatedFilter.php on line 31.
 1 APPPATH/Filters/AuthenticatedFilter.php(31): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 08:55:46 --> ErrorException: file_get_contents(/tmp/flag.txt): Failed to open stream: No such file or directory
in APPPATH/Controllers/ProfileController.php on line 16.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 APPPATH/Controllers/ProfileController.php(16): file_get_contents()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 08:56:19 --> ErrorException: file_get_contents(/tmp/flag.txt): Failed to open stream: No such file or directory
in APPPATH/Controllers/ProfileController.php on line 16.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 APPPATH/Controllers/ProfileController.php(16): file_get_contents()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 08:57:25 --> ErrorException: file_get_contents(/tmp/flag.txt): Failed to open stream: No such file or directory
in APPPATH/Controllers/ProfileController.php on line 16.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 APPPATH/Controllers/ProfileController.php(16): file_get_contents()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 08:57:41 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Filters/AuthenticatedFilter.php(40): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 08:57:49 --> ErrorException: file_get_contents(/tmp/flag.txt): Failed to open stream: No such file or directory
in APPPATH/Controllers/ProfileController.php on line 16.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 APPPATH/Controllers/ProfileController.php(16): file_get_contents()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 08:58:16 --> ErrorException: file_get_contents(/tmp/flag.txt): Failed to open stream: No such file or directory
in APPPATH/Controllers/ProfileController.php on line 16.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 APPPATH/Controllers/ProfileController.php(16): file_get_contents()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 08:59:00 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Filters/AuthenticatedFilter.php(40): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 08:59:30 --> ErrorException: file_get_contents(flag.txt): Failed to open stream: No such file or directory
in APPPATH/Controllers/ProfileController.php on line 16.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 APPPATH/Controllers/ProfileController.php(16): file_get_contents()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:00:04 --> ErrorException: file_get_contents(flag.txt): Failed to open stream: No such file or directory
in APPPATH/Controllers/ProfileController.php on line 16.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 APPPATH/Controllers/ProfileController.php(16): file_get_contents()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:00:08 --> ErrorException: file_get_contents(flag.txt): Failed to open stream: No such file or directory
in APPPATH/Controllers/ProfileController.php on line 16.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 APPPATH/Controllers/ProfileController.php(16): file_get_contents()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:00:25 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Filters/AuthenticatedFilter.php(40): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:00:26 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Filters/AuthenticatedFilter.php(40): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:00:37 --> ErrorException: file_get_contents(flag.txt): Failed to open stream: No such file or directory
in APPPATH/Controllers/ProfileController.php on line 16.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 APPPATH/Controllers/ProfileController.php(16): file_get_contents()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:01:22 --> ErrorException: file_get_contents(/tmp/flag.txt): Failed to open stream: No such file or directory
in APPPATH/Controllers/ProfileController.php on line 16.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 APPPATH/Controllers/ProfileController.php(16): file_get_contents()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:01:25 --> ErrorException: file_get_contents(/tmp/flag.txt): Failed to open stream: No such file or directory
in APPPATH/Controllers/ProfileController.php on line 16.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 APPPATH/Controllers/ProfileController.php(16): file_get_contents()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:01:41 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Filters/AuthenticatedFilter.php(40): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:04:31 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Filters/AuthenticatedFilter.php(40): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:04:44 --> ErrorException: Undefined variable $username
in APPPATH/Views/ProfilePage.php on line 25.
 1 APPPATH/Views/ProfilePage.php(25): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/View/View.php(228): include('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Views/ProfilePage.php')
 3 SYSTEMPATH/View/View.php(231): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH/Common.php(1178): CodeIgniter\View\View->render()
 5 APPPATH/Controllers/ProfileController.php(25): view()
 6 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 7 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 8 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 9 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:06:21 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Filters/AuthenticatedFilter.php(40): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:07:44 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Filters/AuthenticatedFilter.php(40): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:09:15 --> ErrorException: Undefined array key "token"
in APPPATH/Filters/AuthenticatedFilter.php on line 31.
 1 APPPATH/Filters/AuthenticatedFilter.php(31): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:11:07 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Filters/AuthenticatedFilter.php(40): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:11:15 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Filters/AuthenticatedFilter.php(40): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:11:28 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Filters/AuthenticatedFilter.php(40): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:11:30 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Filters/AuthenticatedFilter.php(40): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:11:54 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Controllers/ProfileController.php(19): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:11:55 --> Firebase\JWT\ExpiredException: Expired token
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 162.
 1 APPPATH/Controllers/ProfileController.php(19): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:11:59 --> ErrorException: Undefined array key "token"
in APPPATH/Filters/AuthenticatedFilter.php on line 31.
 1 APPPATH/Filters/AuthenticatedFilter.php(31): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:12:00 --> ErrorException: Undefined array key "token"
in APPPATH/Filters/AuthenticatedFilter.php on line 31.
 1 APPPATH/Filters/AuthenticatedFilter.php(31): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:12:08 --> ErrorException: Undefined array key "token"
in APPPATH/Filters/AuthenticatedFilter.php on line 31.
 1 APPPATH/Filters/AuthenticatedFilter.php(31): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:12:34 --> ErrorException: Undefined array key "token"
in APPPATH/Filters/AuthenticatedFilter.php on line 31.
 1 APPPATH/Filters/AuthenticatedFilter.php(31): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:12:35 --> ErrorException: Undefined array key "token"
in APPPATH/Filters/AuthenticatedFilter.php on line 31.
 1 APPPATH/Filters/AuthenticatedFilter.php(31): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:12:42 --> ErrorException: Undefined array key "token"
in APPPATH/Filters/AuthenticatedFilter.php on line 31.
 1 APPPATH/Filters/AuthenticatedFilter.php(31): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:12:43 --> ErrorException: Undefined array key "token"
in APPPATH/Filters/AuthenticatedFilter.php on line 31.
 1 APPPATH/Filters/AuthenticatedFilter.php(31): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:13:47 --> ErrorException: Undefined array key "token"
in APPPATH/Filters/AuthenticatedFilter.php on line 31.
 1 APPPATH/Filters/AuthenticatedFilter.php(31): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:13:48 --> ErrorException: Undefined array key "token"
in APPPATH/Filters/AuthenticatedFilter.php on line 31.
 1 APPPATH/Filters/AuthenticatedFilter.php(31): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:13:54 --> ErrorException: Undefined array key "token"
in APPPATH/Filters/AuthenticatedFilter.php on line 31.
 1 APPPATH/Filters/AuthenticatedFilter.php(31): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:13:55 --> ErrorException: Undefined array key "token"
in APPPATH/Filters/AuthenticatedFilter.php on line 31.
 1 APPPATH/Filters/AuthenticatedFilter.php(31): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:14:36 --> ErrorException: Undefined array key "token"
in APPPATH/Filters/AuthenticatedFilter.php on line 31.
 1 APPPATH/Filters/AuthenticatedFilter.php(31): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:16:17 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 140.
 1 APPPATH/Filters/AuthenticatedFilter.php(37): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:16:26 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 140.
 1 APPPATH/Filters/AuthenticatedFilter.php(37): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:16:49 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 140.
 1 APPPATH/Filters/AuthenticatedFilter.php(37): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:17:29 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 140.
 1 APPPATH/Filters/AuthenticatedFilter.php(37): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:17:51 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 140.
 1 APPPATH/Filters/AuthenticatedFilter.php(37): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:18:16 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 140.
 1 APPPATH/Filters/AuthenticatedFilter.php(37): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:19:05 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 140.
 1 APPPATH/Filters/AuthenticatedFilter.php(38): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:19:07 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 140.
 1 APPPATH/Filters/AuthenticatedFilter.php(38): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:19:13 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 140.
 1 APPPATH/Filters/AuthenticatedFilter.php(38): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:21:28 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 140.
 1 APPPATH/Filters/AuthenticatedFilter.php(38): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/Filters/Filters.php(184): App\Filters\AuthenticatedFilter->before()
 3 SYSTEMPATH/CodeIgniter.php(474): CodeIgniter\Filters\Filters->run()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:23:05 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 140.
 1 APPPATH/Controllers/ProfileController.php(19): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:23:07 --> Firebase\JWT\SignatureInvalidException: Signature verification failed
in VENDORPATH/firebase/php-jwt/src/JWT.php on line 140.
 1 APPPATH/Controllers/ProfileController.php(19): Firebase\JWT\JWT::decode()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:45:15 --> ErrorException: file_get_contents(/tmp/flag.txt): Failed to open stream: No such file or directory
in APPPATH/Controllers/ProfileController.php on line 16.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 APPPATH/Controllers/ProfileController.php(16): file_get_contents()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:45:55 --> ErrorException: file_get_contents(/tmp/flag.txt): Failed to open stream: No such file or directory
in APPPATH/Controllers/ProfileController.php on line 16.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 APPPATH/Controllers/ProfileController.php(16): file_get_contents()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:50:59 --> ErrorException: file_get_contents(/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/flag.txt): Failed to open stream: No such file or directory
in APPPATH/Controllers/ProfileController.php on line 17.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 APPPATH/Controllers/ProfileController.php(17): file_get_contents()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:51:23 --> Error: Undefined constant "App\Controllers\BASEPATH"
in APPPATH/Controllers/ProfileController.php on line 17.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 09:52:14 --> ErrorException: file_get_contents(/mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/flag.txt): Failed to open stream: No such file or directory
in APPPATH/Controllers/ProfileController.php on line 17.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 APPPATH/Controllers/ProfileController.php(17): file_get_contents()
 3 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\ProfileController->index()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 6 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 10:12:16 --> Error: Call to a member function getWhere() on null
in APPPATH/Controllers/UserController.php on line 58.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UserController->register()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 10:14:32 --> TypeError: CodeIgniter\Database\BaseBuilder::get(): Argument #1 ($limit) must be of type ?int, string given, called in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Controllers/UserController.php on line 58
in SYSTEMPATH/Database/BaseBuilder.php on line 1605.
 1 APPPATH/Controllers/UserController.php(58): CodeIgniter\Database\BaseBuilder->get()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UserController->register()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 10:17:44 --> ErrorException: Undefined property: App\Controllers\UserController::$db
in APPPATH/Controllers/UserController.php on line 58.
 1 APPPATH/Controllers/UserController.php(58): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UserController->register()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 10:18:04 --> ErrorException: Undefined property: App\Controllers\UserController::$db
in APPPATH/Controllers/UserController.php on line 58.
 1 APPPATH/Controllers/UserController.php(58): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UserController->register()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 5 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 10:18:12 --> Error: Call to undefined method CodeIgniter\Database\MySQLi\Connection::where()
in APPPATH/Controllers/UserController.php on line 58.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UserController->register()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 10:19:46 --> ParseError: syntax error, unexpected token ":", expecting "]"
in APPPATH/Controllers/UserController.php on line 58.
 1 SYSTEMPATH/Autoloader/Autoloader.php(288): CodeIgniter\Autoloader\Autoloader->includeFile()
 2 SYSTEMPATH/Autoloader/Autoloader.php(266): CodeIgniter\Autoloader\Autoloader->loadInNamespace()
 3 [internal function]: CodeIgniter\Autoloader\Autoloader->loadClass()
 4 SYSTEMPATH/CodeIgniter.php(899): class_exists()
 5 SYSTEMPATH/CodeIgniter.php(489): CodeIgniter\CodeIgniter->startController()
 6 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 7 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
ERROR - 2024-01-23 10:22:37 --> mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ':0:' at line 3 in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:306
Stack trace:
#0 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(306): mysqli->query()
#1 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
#4 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Controllers/UserController.php(58): CodeIgniter\Database\BaseBuilder->getWhere()
#5 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(941): App\Controllers\UserController->register()
#6 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
#7 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
#8 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php(79): CodeIgniter\CodeIgniter->run()
#9 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(47): require_once('...')
#10 {main}
CRITICAL - 2024-01-23 10:22:37 --> Error: Call to a member function getRowArray() on bool
in APPPATH/Controllers/UserController.php on line 58.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UserController->register()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 10:25:20 --> Error: Call to undefined method CodeIgniter\Database\MySQLi\Connection::select()
in APPPATH/Controllers/UserController.php on line 58.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UserController->register()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 10:25:38 --> Error: Call to a member function pluck() on array
in APPPATH/Controllers/UserController.php on line 58.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UserController->register()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
CRITICAL - 2024-01-23 10:25:43 --> Error: Call to a member function pluck() on array
in APPPATH/Controllers/UserController.php on line 58.
 1 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UserController->register()
 2 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 4 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
ERROR - 2024-01-23 14:06:46 --> Error connecting to the database: mysqli_sql_exception: Connection timed out in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:185
Stack trace:
#0 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(185): mysqli->real_connect()
#1 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(392): CodeIgniter\Database\MySQLi\Connection->connect()
#2 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(575): CodeIgniter\Database\BaseConnection->initialize()
#3 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
#4 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Controllers/UserController.php(23): CodeIgniter\Database\BaseBuilder->getWhere()
#5 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(941): App\Controllers\UserController->login()
#6 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
#7 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
#8 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php(79): CodeIgniter\CodeIgniter->run()
#9 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(47): require_once('...')
#10 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Connection timed out in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:229
Stack trace:
#0 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(392): CodeIgniter\Database\MySQLi\Connection->connect()
#1 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(575): CodeIgniter\Database\BaseConnection->initialize()
#2 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
#3 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Controllers/UserController.php(23): CodeIgniter\Database\BaseBuilder->getWhere()
#4 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(941): App\Controllers\UserController->login()
#5 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
#6 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
#7 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php(79): CodeIgniter\CodeIgniter->run()
#8 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(47): require_once('...')
#9 {main}
CRITICAL - 2024-01-23 14:06:46 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Connection timed out
in SYSTEMPATH/Database/BaseConnection.php on line 428.
 1 SYSTEMPATH/Database/BaseConnection.php(575): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH/Database/BaseBuilder.php(1746): CodeIgniter\Database\BaseConnection->query()
 3 APPPATH/Controllers/UserController.php(23): CodeIgniter\Database\BaseBuilder->getWhere()
 4 SYSTEMPATH/CodeIgniter.php(941): App\Controllers\UserController->login()
 5 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController()
 6 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 7 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
ERROR - 2024-01-23 14:29:36 --> Error connecting to the database: mysqli_sql_exception: Connection timed out in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:185
Stack trace:
#0 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(185): mysqli->real_connect()
#1 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(392): CodeIgniter\Database\MySQLi\Connection->connect()
#2 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(575): CodeIgniter\Database\BaseConnection->initialize()
#3 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1613): CodeIgniter\Database\BaseConnection->query()
#4 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Config/Routes.php(18): CodeIgniter\Database\BaseBuilder->get()
#5 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(890): CodeIgniter\Router\RouteCollection->{closure}()
#6 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(489): CodeIgniter\CodeIgniter->startController()
#7 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
#8 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php(79): CodeIgniter\CodeIgniter->run()
#9 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(47): require_once('...')
#10 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Connection timed out in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:229
Stack trace:
#0 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(392): CodeIgniter\Database\MySQLi\Connection->connect()
#1 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(575): CodeIgniter\Database\BaseConnection->initialize()
#2 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1613): CodeIgniter\Database\BaseConnection->query()
#3 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Config/Routes.php(18): CodeIgniter\Database\BaseBuilder->get()
#4 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(890): CodeIgniter\Router\RouteCollection->{closure}()
#5 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(489): CodeIgniter\CodeIgniter->startController()
#6 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
#7 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php(79): CodeIgniter\CodeIgniter->run()
#8 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(47): require_once('...')
#9 {main}
CRITICAL - 2024-01-23 14:29:36 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Connection timed out
in SYSTEMPATH/Database/BaseConnection.php on line 428.
 1 SYSTEMPATH/Database/BaseConnection.php(575): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH/Database/BaseBuilder.php(1613): CodeIgniter\Database\BaseConnection->query()
 3 APPPATH/Config/Routes.php(18): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH/CodeIgniter.php(890): CodeIgniter\Router\RouteCollection->{closure}()
 5 SYSTEMPATH/CodeIgniter.php(489): CodeIgniter\CodeIgniter->startController()
 6 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 7 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
ERROR - 2024-01-23 14:29:52 --> Error connecting to the database: mysqli_sql_exception: Connection timed out in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:185
Stack trace:
#0 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(185): mysqli->real_connect()
#1 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(392): CodeIgniter\Database\MySQLi\Connection->connect()
#2 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(575): CodeIgniter\Database\BaseConnection->initialize()
#3 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1613): CodeIgniter\Database\BaseConnection->query()
#4 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Config/Routes.php(18): CodeIgniter\Database\BaseBuilder->get()
#5 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(890): CodeIgniter\Router\RouteCollection->{closure}()
#6 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(489): CodeIgniter\CodeIgniter->startController()
#7 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
#8 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php(79): CodeIgniter\CodeIgniter->run()
#9 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(47): require_once('...')
#10 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Connection timed out in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:229
Stack trace:
#0 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(392): CodeIgniter\Database\MySQLi\Connection->connect()
#1 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(575): CodeIgniter\Database\BaseConnection->initialize()
#2 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1613): CodeIgniter\Database\BaseConnection->query()
#3 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Config/Routes.php(18): CodeIgniter\Database\BaseBuilder->get()
#4 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(890): CodeIgniter\Router\RouteCollection->{closure}()
#5 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(489): CodeIgniter\CodeIgniter->startController()
#6 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
#7 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php(79): CodeIgniter\CodeIgniter->run()
#8 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(47): require_once('...')
#9 {main}
CRITICAL - 2024-01-23 14:29:52 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Connection timed out
in SYSTEMPATH/Database/BaseConnection.php on line 428.
 1 SYSTEMPATH/Database/BaseConnection.php(575): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH/Database/BaseBuilder.php(1613): CodeIgniter\Database\BaseConnection->query()
 3 APPPATH/Config/Routes.php(18): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH/CodeIgniter.php(890): CodeIgniter\Router\RouteCollection->{closure}()
 5 SYSTEMPATH/CodeIgniter.php(489): CodeIgniter\CodeIgniter->startController()
 6 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 7 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
ERROR - 2024-01-23 14:30:08 --> Error connecting to the database: mysqli_sql_exception: Connection timed out in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:185
Stack trace:
#0 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(185): mysqli->real_connect()
#1 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(392): CodeIgniter\Database\MySQLi\Connection->connect()
#2 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(575): CodeIgniter\Database\BaseConnection->initialize()
#3 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1613): CodeIgniter\Database\BaseConnection->query()
#4 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Config/Routes.php(18): CodeIgniter\Database\BaseBuilder->get()
#5 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(890): CodeIgniter\Router\RouteCollection->{closure}()
#6 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(489): CodeIgniter\CodeIgniter->startController()
#7 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
#8 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php(79): CodeIgniter\CodeIgniter->run()
#9 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(47): require_once('...')
#10 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Connection timed out in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:229
Stack trace:
#0 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(392): CodeIgniter\Database\MySQLi\Connection->connect()
#1 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(575): CodeIgniter\Database\BaseConnection->initialize()
#2 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1613): CodeIgniter\Database\BaseConnection->query()
#3 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Config/Routes.php(18): CodeIgniter\Database\BaseBuilder->get()
#4 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(890): CodeIgniter\Router\RouteCollection->{closure}()
#5 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(489): CodeIgniter\CodeIgniter->startController()
#6 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
#7 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php(79): CodeIgniter\CodeIgniter->run()
#8 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(47): require_once('...')
#9 {main}
CRITICAL - 2024-01-23 14:30:08 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Connection timed out
in SYSTEMPATH/Database/BaseConnection.php on line 428.
 1 SYSTEMPATH/Database/BaseConnection.php(575): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH/Database/BaseBuilder.php(1613): CodeIgniter\Database\BaseConnection->query()
 3 APPPATH/Config/Routes.php(18): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH/CodeIgniter.php(890): CodeIgniter\Router\RouteCollection->{closure}()
 5 SYSTEMPATH/CodeIgniter.php(489): CodeIgniter\CodeIgniter->startController()
 6 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 7 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
ERROR - 2024-01-23 14:30:31 --> Error connecting to the database: mysqli_sql_exception: Connection timed out in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:185
Stack trace:
#0 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(185): mysqli->real_connect()
#1 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(392): CodeIgniter\Database\MySQLi\Connection->connect()
#2 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(575): CodeIgniter\Database\BaseConnection->initialize()
#3 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1613): CodeIgniter\Database\BaseConnection->query()
#4 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Config/Routes.php(18): CodeIgniter\Database\BaseBuilder->get()
#5 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(890): CodeIgniter\Router\RouteCollection->{closure}()
#6 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(489): CodeIgniter\CodeIgniter->startController()
#7 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
#8 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php(79): CodeIgniter\CodeIgniter->run()
#9 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(47): require_once('...')
#10 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Connection timed out in /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:229
Stack trace:
#0 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(392): CodeIgniter\Database\MySQLi\Connection->connect()
#1 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseConnection.php(575): CodeIgniter\Database\BaseConnection->initialize()
#2 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1613): CodeIgniter\Database\BaseConnection->query()
#3 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/app/Config/Routes.php(18): CodeIgniter\Database\BaseBuilder->get()
#4 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(890): CodeIgniter\Router\RouteCollection->{closure}()
#5 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(489): CodeIgniter\CodeIgniter->startController()
#6 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
#7 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php(79): CodeIgniter\CodeIgniter->run()
#8 /mnt/d/CTF/Idea/CodeIgniter/Insomnia/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(47): require_once('...')
#9 {main}
CRITICAL - 2024-01-23 14:30:31 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Connection timed out
in SYSTEMPATH/Database/BaseConnection.php on line 428.
 1 SYSTEMPATH/Database/BaseConnection.php(575): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH/Database/BaseBuilder.php(1613): CodeIgniter\Database\BaseConnection->query()
 3 APPPATH/Config/Routes.php(18): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH/CodeIgniter.php(890): CodeIgniter\Router\RouteCollection->{closure}()
 5 SYSTEMPATH/CodeIgniter.php(489): CodeIgniter\CodeIgniter->startController()
 6 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest()
 7 FCPATH/index.php(79): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Commands/Server/rewrite.php(47): require_once('/mnt/d/CTF/Idea/CodeIgniter/Insomnia/public/index.php')
